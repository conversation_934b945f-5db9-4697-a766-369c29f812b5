import {
  Controller,
  Post,
  Get,
  Body,
  Query,
  Headers,
  HttpException,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

import { Res } from 'src/common/dto/res.dto';
import { ErrorCode } from 'src/error/error-code';

import { ChatService } from './chat.service';
import {
  CreateSessionDto,
  CreateSessionResponseDto,
  GetHistorySessionsDto,
  GetHistorySessionsResponseDto,
  GetHistoryMessagesDto,
  GetHistoryMessagesResponseDto,
  FeedbackDto,
  FeedbackResponseDto,
} from './dto';

@ApiTags('聊天会话管理')
@Controller('/v1/chat')
export class ChatController {
  constructor(private readonly chatService: ChatService) {}

  @Post('/createSession')
  @ApiOperation({
    summary: '创建会话',
    description: '初始化创建一个新的聊天会话',
  })
  @ApiResponse({
    status: 200,
    description: '创建会话成功',
    type: CreateSessionResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 500,
    description: '服务器内部错误',
  })
  async createSession(
    @Body() payload: CreateSessionDto,
    @Headers() headers: any
  ) {
    try {
      console.log('Controller: 收到创建会话请求', JSON.stringify(payload));

      const result = await this.chatService.createSession(payload, headers);
      console.log('Controller: 创建会话成功', JSON.stringify(result));
      return new Res().success(result);
    } catch (error) {
      console.error('Controller: 创建会话失败:', error.message || error);
      console.error('Controller: 错误详情:', error.response?.data || error);

      if (error.response) {
        // 转发上游服务的错误响应
        throw new HttpException(error.response.data, error.response.status);
      }

      // 如果是验证错误或其他HTTP异常，直接重新抛出
      if (error instanceof HttpException) {
        throw error;
      }

      return new Res().error(new ErrorCode().DEMO_UNKNOWN, '创建会话失败');
    }
  }

  @Get('/getHistorySessions')
  @ApiOperation({
    summary: '获取历史会话列表',
    description: '获取用户的历史聊天会话列表',
  })
  @ApiResponse({
    status: 200,
    description: '获取历史会话列表成功',
    type: GetHistorySessionsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 500,
    description: '服务器内部错误',
  })
  async getHistorySessions(
    @Query() params: GetHistorySessionsDto,
    @Headers() headers: any
  ) {
    try {
      const result = await this.chatService.getHistorySessions(params, headers);
      return new Res().success(result);
    } catch (error) {
      console.error('获取历史会话列表失败:', error);
      if (error.response) {
        // 转发上游服务的错误响应
        throw new HttpException(error.response.data, error.response.status);
      }
      return new Res().error(
        new ErrorCode().DEMO_UNKNOWN,
        '获取历史会话列表失败'
      );
    }
  }

  @Get('/getHistoryMessages')
  @ApiOperation({
    summary: '获取历史消息详情',
    description: '分页获取指定会话的历史消息详情',
  })
  @ApiResponse({
    status: 200,
    description: '获取历史消息详情成功',
    type: GetHistoryMessagesResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 500,
    description: '服务器内部错误',
  })
  async getHistoryMessages(
    @Query() params: GetHistoryMessagesDto,
    @Headers() headers: any
  ) {
    try {
      const result = await this.chatService.getHistoryMessages(params, headers);
      return new Res().success(result);
    } catch (error) {
      console.error('获取历史消息详情失败:', error);
      if (error.response) {
        // 转发上游服务的错误响应
        throw new HttpException(error.response.data, error.response.status);
      }
      return new Res().error(
        new ErrorCode().DEMO_UNKNOWN,
        '获取历史消息详情失败'
      );
    }
  }

  @Post('/feedback')
  @ApiOperation({
    summary: '点赞/点踩接口',
    description: '对AI回答进行点赞或点踩反馈',
  })
  @ApiResponse({
    status: 200,
    description: '反馈操作成功',
    type: FeedbackResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 500,
    description: '服务器内部错误',
  })
  async feedback(@Body() payload: FeedbackDto, @Headers() headers: any) {
    try {
      const result = await this.chatService.feedback(payload, headers);
      return new Res().success(result);
    } catch (error) {
      console.error('反馈操作失败:', error);
      if (error.response) {
        // 转发上游服务的错误响应
        throw new HttpException(error.response.data, error.response.status);
      }
      return new Res().error(new ErrorCode().DEMO_UNKNOWN, '反馈操作失败');
    }
  }
}
